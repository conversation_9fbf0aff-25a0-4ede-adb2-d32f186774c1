require 'application_responder'

class ApplicationController < ActionController::Base
  include DeviseTokenAuth::Concerns::SetUserByToken
  include Punditry::Controller
  include Rails::Pagination

  skip_authorization if: :devise_controller? # bo inaczej ginie token po odswiezeniu strony

  before_action :set_paper_trail_whodunnit

  skip_after_action :verify_authorized, only: [:account_confirmation_success, :spa]
  before_action :set_paper_trail_whodunnit

  after_action :bypass_etag

  def bypass_etag
    headers['Last-Modified'] = Time.now.httpdate
  end

  def account_confirmation_success
  end

  def spa
    render plain: "You won\'t see this in production environment!"
  end
end
