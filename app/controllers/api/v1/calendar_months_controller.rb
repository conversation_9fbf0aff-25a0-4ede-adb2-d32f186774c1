module Api
  module V1
    class Calendar<PERSON>onthsController < ApiController
      before_action :authorize_action
      before_action :set_default_calendar_months, only: :index

      def index
        @calendar_months = search(scope: scope, filters: params[:f]).results.includes(:user)
                                                                    .joins(:user).order('users.department_id ASC')
        return unless params[:fill_default]

        set_year_and_month
        add_default_calendar_months
        sort_calendar_months
      end

      def form_data
        @year, @month = params.require(%i[year month]).map(&:to_i)
        default_calendar_month_generator = DefaultCalendarMonthGenerator.new(@year, @month)
        @pl_full_time = default_calendar_month_generator.pl_full_time
        @pl_half_time = default_calendar_month_generator.pl_half_time
        @rb_full_time = default_calendar_month_generator.rb_full_time
      end

      def bulk_update
        CalendarMonth.transaction do
          calendar_months_params.each do |calendar_month_params|
            calendar_month = CalendarMonth.find_or_initialize_by(calendar_month_params.slice(:user_id, :year, :month))
            authorize_update(calendar_month)
            calendar_month.update!(calendar_month_params)
          end
        end

        head :created
      end

      private

      def set_year_and_month
        @year, @month = params.require(:f).require(%i[year month]).map(&:to_i)

        head :bad_request unless @year.present? && @month.present?
      end

      def add_default_calendar_months
        @calendar_months = @calendar_months.to_a
        users = users_search(scope: users_scope, filters: params[:f]).results.distinct
        (users - @calendar_months.map(&:user)).each do |user|
          @calendar_months << DefaultCalendarMonthGenerator.new(@year, @month).pl_full_time.tap do |calendar_month|
            calendar_month.user = user
          end
        end
      end

      def sort_calendar_months
        @calendar_months.sort_by! { |calendar_month| [calendar_month.user.department_id, calendar_month.user.id] }
      end

      def users_scope
        base_scope = User.active.native.where.not(system: true).where.not(department_id: nil).order(department_id: :asc)
        CalendarMonthPolicy::Scope.new(current_user, base_scope).users_scope
      end

      def authorize_action
        authorize(CalendarMonth)
      end

      def authorize_update(calendar_month)
        authorize(calendar_month, :update?)
      end

      def search(options = {})
        ::Searches::CalendarMonthSearch.new(search_options(options), current_user)
      end

      def users_search(options = {})
        ::Searches::UserSearch.new(search_options(options), current_user)
      end

      def scope
        policy_scope(CalendarMonth)
      end

      def calendar_months_params
        calendar_months_params = params.permit(calendar_months: [:user_id, :year, :month, { days: {} }])
        calendar_months_params = [calendar_months_params.require(:calendar_months)].flatten
        calendar_months_params.each do |calendar_month_params|
          calendar_month_params.require(%i[user_id year month])
        end

        calendar_months_params
      end

      def set_default_calendar_months # rubocop:disable Metrics/AbcSize
        return if params.dig(:f, :years_months).blank?

        years_months = params[:f][:years_months].map { |year_month| year_month.split('-') }
        params[:f][:years_months] = years_months
        @default_calendar_months = years_months.map do |year, month|
          DefaultCalendarMonthGenerator.new(year.to_i, month.to_i).pl_full_time
        end
      end
    end
  end
end
