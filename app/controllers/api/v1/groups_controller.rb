module Api
  module V1
    class GroupsController < ApiController
      include ::Api::V1::Concerns::Documentation::GroupsEndpoint

      def index
        authorize(Group, :index?)
        results = search(scope: groups_scope, filters: params[:f]).results
        request.variant = :collection_for_select if params[:f].try(:[], :collection_for_select).to_s == 'true'
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              if params[:page].present? || params[:per_page].present?
                paginate_opts = params.dup
                paginate_opts[:filters] ||= {}
                paginate_opts[:filters][:paginated_collection_for_select] = true
                @groups = paginate(authorize!(results), search_options(paginate_opts))
              else
                @groups = authorize!(results)
              end
              render template: '/api/v1/groups/index', collection: @groups
            end
            variant.none do
              @groups = paginate(authorize!(results), search_options(params))
              raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @groups.empty?
            end
          end
        end
      end

      def show
        @group = authorize!(find_group)
        @projects = projects
        respond_with(@group.decorate)
      end

      def create
        authorize!(Group)
        group = Group.new(group_params)
        group.save
        respond_with(group.decorate)
      end

      def update
        @group = authorize!(find_group)
        @group.update(group_params)
        respond_with(@group.decorate)
      end

      def destroy
        @group = authorize!(find_group)
        @group.destroy
        respond_with(@group.decorate)
      end

      private

      def projects
        opts = { scope: policy_scope(Project).of_group(@group),
                 filters: params[:f] }
        Searches::ProjectSearch.new(search_options(opts), current_user).results
      end

      def search(options = {})
        @search = ::Searches::GroupSearch.new(search_options(options))
      end

      def groups_scope
        Group.includes(:active_users)
      end

      def find_group
        policy_scope(Group).find(params[:id])
      end

      def find_groups
        groups_scope.find(params[:ids])
      end

      def group_params
        params.require(:group).permit(policy(Group).permitted_attributes)
      end
    end
  end
end
