module Api
  module V1
    class ContractorsController < ApiController
      before_action :authorize
      before_action :find_contractor, except: %i[create index]

      def create
        contractor = Contractor.new(contractor_params)
        contractor.activate if policy(contractor).activate?
        contractor.created_by = current_user
        contractor.save
        respond_with(contractor)
      end

      def update
        @contractor.update(contractor_params)
        respond_with(@contractor)
      end

      def show; end

      def index
        @contractors = search(scope: policy_scope(Contractor), filters: params[:f]).results
      end

      def activate
        @contractor.activate!

        respond_with @contractor
      end

      def destroy # rubocop:disable Metrics/MethodLength
        unless @contractor.pending?
          render json: { error: 'Active Contractor cannot be deleted.' }, status: :unprocessable_entity and return
        end

        unless @contractor.all_cost_invoices_deleted?
          render json: { error: 'Contractor has invoices and cannot be deleted.' }, status: :unprocessable_entity and return
        end

        ActiveRecord::Base.transaction do
          @contractor.cost_invoices.map do |ci|
            ci.update_attribute(:contractor_id, nil) # rubocop:disable Rails/SkipsModelValidations
            ci.update_attribute(:removed_contractor_data, @contractor.as_json) # rubocop:disable Rails/SkipsModelValidations
          end

          @contractor.reload.destroy!
          head :no_content
        end
      end

      private

      def authorize
        super(Contractor)
      end

      def find_contractor
        @contractor = policy_scope(Contractor).find(params[:id])
      end

      def contractor_params
        params.require(:contractor).permit(:user_id, :name, :street, :street_number, :apartment,
                                           :additional_address, :city, :postcode, :post, :country,
                                           :voivodeship, :district, :community, :vat_number,
                                           :account_number, :vat_payer, :download_from_gus)
      end

      def search(options = {})
        Searches::ContractorSearch.new(search_options(options))
      end
    end
  end
end
