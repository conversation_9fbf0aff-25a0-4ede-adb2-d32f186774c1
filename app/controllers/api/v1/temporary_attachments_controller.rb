module Api
  module V1
    class TemporaryAttachmentsController < ApiController
      respond_to :json, :multipart_form

      def create
        authorize(Attachment)
        attachment = Tempfile.create(temporary_attachment_params[:file].original_filename)
        create_temporary_file(temporary_attachment_params[:file], attachment)
        render json: {
          filename: temporary_attachment_params[:file].original_filename,
          location: attachment.path,
          file: temporary_attachment_params[:file]
        }, status: :created
      end

      private

      def create_temporary_file(file, attachment)
        attachment.binmode
        attachment << file.read
        attachment.rewind
        attachment.close
      end

      def temporary_attachment_params
        params.permit(:file)
      end
    end
  end
end
