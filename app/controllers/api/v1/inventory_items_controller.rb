module Api
  module V1
    class InventoryItemsController < ApiController
      respond_to :json
      before_action :find_inventory_item, except: %i[index create]
      before_action :authorize

      def create
        inventory_item = InventoryItem.new(inventory_item_params.merge(requester: current_user))
        inventory_item.save
        respond_with inventory_item.decorate
      end

      def index
        scope = search(scope: InventoryItem.includes(:user, :company), filters: params[:f])
        respond_with paginate(policy_scope(scope.results)).decorate
      end

      def show
        respond_with @inventory_item
      end

      def update
        @inventory_item.update(inventory_item_params)
        respond_with @inventory_item.decorate
      end

      def activate
        transition_and_render(:activate!)
      end

      def reject
        transition_and_render(:reject!)
      end

      def close
        transition_and_render(:close!)
      end

      private

      def search(options = {})
        @search = ::Searches::InventoryItemSearch.new(search_options(options))
      end

      def transition_and_render(action)
        if @inventory_item.public_send(action)
          head :no_content
        else
          render json: { errors: @inventory_item.errors }, status: :bad_request
        end
      end

      def find_inventory_item
        @inventory_item = policy_scope(InventoryItem).find(params[:id])
      end

      def inventory_item_params
        params.require(:inventory_item).permit(:user_id, :name, :description, :handed_on,
                                               :inventory_no, :company_id, :comment, :location)
      end

      def authorize
        super(@inventory_item || InventoryItem)
      end
    end
  end
end
