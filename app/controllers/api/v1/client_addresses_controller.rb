module Api
  module V1
    class ClientAddressesController < ClientsController
      before_action :find_client
      before_action :authorize_manage, except: %i[index show]
      before_action :find_client_address, only: %i[show update destroy]
      skip_after_action :verify_policy_scoped

      def index
        authorize @client
        @client_addresses = @client.client_addresses
      end

      def show
        authorize(@client, :index?)
      end

      def create
        client_address = @client.client_addresses.create(client_address_params)
        respond_with(@client, client_address)
      end

      def update
        @client_address.update(client_address_params)
        respond_with(@client, @client_address)
      end

      def destroy
        @client_address.destroy
        head :no_content
      end

      private

      def client_address_params
        params.require(:client_address)
              .permit(:identifier, :name, :street, :street_number, :additional_address, :apartment,
                      :city, :postcode, :post, :voivodeship, :district, :community, :country,
                      :vat_number, :invoice_sending_method, :invoice_sending_email, :download_from_gus,
                      :invoice_sending_email_receiver)
      end

      def find_client
        @client = Client.find(params[:client_id])
      end

      def find_client_address
        @client_address = @client.client_addresses.find(params[:id])
      end
    end
  end
end
