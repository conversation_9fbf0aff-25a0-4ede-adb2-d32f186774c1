module Api
  module V1
    class CompaniesController < ApiController
      include ::Api::V1::Concerns::Documentation::CompaniesEndpoint

      def index
        authorize(Company, :index?)
        results = search(scope: companies_scope, filters: params[:f]).results
        @companies = paginate(authorize!(results), search_options(params))
        raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @companies.empty?
        respond_with(@companies)
      end

      def show
        @company = authorize!(find_company)
        respond_with(@company.decorate)
      end

      def create
        authorize!(Company)
        respond_with(Company.create(company_params).decorate)
      end

      def update
        @company = authorize!(find_company)
        @company.update(company_params)
        respond_with(@company.decorate)
      end

      def destroy
        @company = authorize!(find_company)
        @company.destroy
        respond_with(@company.decorate)
      end

      private

      def search(options = {})
        @search = ::Searches::CompanySearch.new(search_options(options))
      end

      def companies_scope
        params[:native] ? Company.native : Company.all
      end

      def find_company
        Company.find(params[:id])
      end

      def company_params
        params.require(:company).permit(policy(Company).permitted_attributes)
      end
    end
  end
end
