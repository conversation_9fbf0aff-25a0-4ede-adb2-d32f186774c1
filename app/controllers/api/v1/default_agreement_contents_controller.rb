module Api
  module V1
    class DefaultAgreementContentsController < ApiController
      def index
        authorize(DefaultAgreementContent)
        @default_agreement_contents = policy_scope(DefaultAgreementContent.all).decorate
      end

      def update
        authorize(DefaultAgreementContent)
        @default_agreement_content = DefaultAgreementContent.find(params[:id])
        if @default_agreement_content.update(default_agreement_content_params)
          head :no_content
        else
          render json: @default_agreement_content.errors.messages, status: :unprocessable_entity
        end
      end

      private

      def default_agreement_content_params
        params.require(:default_agreement_contents).permit(:company_id, :business_to_business, :content)
      end
    end
  end
end
