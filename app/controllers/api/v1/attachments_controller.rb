module Api
  module V1
    class AttachmentsController < ApiController
      respond_to :json, :multipart_form

      def create
        authorize!(Attachment)
        attachment = Attachment.new(attachment_params)
        if attachment.save
          render json: {
            id: attachment.id,
            location: attachment.file_url
          }, status: :created
        else
          render json: { errors: attachment.errors.messages }, status: :unprocessable_entity
        end
      end

      def bulk_destroy
        authorize(Attachment)
        ids = params[:ids]
        if ids
          attachments = Attachment.where(id: ids.split(',').flatten)
          attachments.destroy_all
        end
        head :no_content
      end

      def file
        authorize(Attachment)
        file = Attachment.find(params[:id]).file
        if file && file.exists?
          download_file(file)
        else
          logger.error "Couldn't find file"
          return render_file_not_found
        end
      end

      private

      def download_file(file)
        path_to_file = Rails.root.join(file.storage.directory + file.id).to_s
        send_file(path_to_file,
                  filename: file.metadata['filename'],
                  type: file.metadata['mime_type'] || 'application/octet-stream',
                  disposition: 'inline')
      end

      def attachment_params
        params.permit(:file)
      end
    end
  end
end
