module Unpaid<PERSON>mail<PERSON>ender
  extend ActiveSupport::Concern

  def send_emails(emails_data)
    emails_data.map { |item| process_email_item(item) }
  end

  private

  def process_email_item(item)
    if item[:emails].blank?
      generate_error_response(item, 'Email is blank')
    elsif invalid_email_format?(item[:emails])
      generate_error_response(item, 'Invalid email format')
    else
      send_email(item)
      generate_success_response(item)
    end
  end

  def invalid_email_format?(emails)
    emails.split(/\s*,\s*/).grep_v(Devise.email_regexp).present?
  end

  def send_email(item)
    InvoicesMailer.unpaid_invoices(
      params: {
        invoices_numbers: item[:invoices_numbers],
        client: item[:client],
        company: item[:company],
        recipients: item[:emails]
      },
      body: item[:content]
    ).deliver_later
  end

  def generate_error_response(item, error_message)
    build_response(item, error: error_message)
  end

  def generate_success_response(item)
    build_response(item, message: 'Email successfully sent')
  end

  def build_response(item, extra)
    {
      client: item[:client],
      invoices_numbers: item[:invoices_numbers],
      email: item[:emails],
      overdue_statuses: item[:overdue_statuses]
    }.merge(extra)
  end
end
