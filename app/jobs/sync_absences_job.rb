require 'date_utils'

class SyncAbsencesJob < ApplicationJob
  def perform(holiday_request, pre_commit_changes)
    return true if skip_holiday_request?(holiday_request, pre_commit_changes)

    if delete_rejected_absences?(holiday_request)
      delete_rejected_absences(holiday_request)
    else
      handle_absences(holiday_request, pre_commit_changes, date_range(holiday_request))
    end
  end

  private

  def handle_absences(holiday_request, pre_commit_changes, range) # rubocop:disable Metrics/MethodLength
    return unless update_existing_absences?(pre_commit_changes)

    new_objects = []
    range.to_a.each do |date|
      next if skip_date?(holiday_request, date)

      new_objects << {
        user_id: holiday_request.applicant_id,
        date: date,
        hours: holiday_request.hours,
        visible: holiday_request.visible,
        category: holiday_request.category
      }
    end
    Absence.transaction do
      holiday_request.absences.delete_all
      holiday_request.absences.create!(new_objects)
    end
  end

  def skip_holiday_request?(holiday_request, pre_commit_changes)
    pre_commit_changes.empty? || holiday_request.destroyed?
  end

  def skip_date?(holiday_request, date)
    !(holiday_request.non_working_day_request? || holiday_request.applicant.working_day?(date))
  end

  def date_range(holiday_request)
    holiday_request.starts_on..holiday_request.ends_on
  end

  def delete_rejected_absences?(holiday_request)
    holiday_request.rejected_at.present?
  end

  def delete_rejected_absences(holiday_request)
    holiday_request.absences.delete_all
  end

  def update_existing_absences?(pre_commit_changes)
    pre_commit_changes.keys.intersect?(%w[absences_count hours starts_on ends_on applicant_id
                                          category visible accepted_at rejected_at non_working_day_request])
  end
end
