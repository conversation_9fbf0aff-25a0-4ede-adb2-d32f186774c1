class InvoicesMailer < PaymentSchedulesMailer
  def processed_invoices(xlsx_doc, file_name)
    attachments[file_name] = xlsx_doc
    recipients = processed_invoices_recipients

    mail(to: recipients, subject: 'Cotygodniowy raport z faktur') if recipients.present?
  end

  def unpaid_invoices(params:, body: nil, use_layout: true)
    prepare_unpaid_invoice_context(params, body)

    invoices = find_invoices(params[:invoices_numbers], params[:client])

    mail(to: params[:recipients], subject: build_unpaid_invoices_subject) do |format|
      attach_invoice_documents(invoices)

      format.html { render template: 'mailers/invoices_mailer/unpaid_invoices', layout: use_layout }
      format.text { render template: 'mailers/invoices_mailer/unpaid_invoices', layout: use_layout }
    end
  end

  private

  def processed_invoices_recipients
    User.active.includes(:global_roles).where(global_roles: { processed_invoices_notification: true })
        .pluck(:email).uniq
  end

  def prepare_unpaid_invoice_context(params, body)
    @invoices_numbers = params[:invoices_numbers].join(', ')
    @client_name = params[:client]
    @company = params[:company]
    @custom_body = body
  end

  def find_invoices(numbers, client_name)
    Invoice.includes(:client).where(number: numbers, client_name: client_name)
  end

  def attach_invoice_documents(invoices)
    invoices.each do |invoice|
      document = invoice.invoice_document&.document
      next unless document

      begin
        filename = document.metadata['filename'] || "#{invoice.number}.pdf"
        attachments[filename] = document.download
      rescue StandardError => e
        Rails.logger.warn "Failed to attach document for invoice #{invoice.number}: #{e.message}"
      end
    end
  end

  def build_unpaid_invoices_subject
    I18n.t('mailers.invoices.unpaid_invoices.subject', invoices_numbers: @invoices_numbers)
  end
end
