class HolidayRequestForm < SimpleDelegator
  include Base::FormObject
  include HolidayRequestForms
  def self.source_class
    HolidayRequest
  end
  validates :category, :'holiday_request_forms/category_selection' => true
  validates_with HolidayRequestForms::UpdateValidator,
                 HolidayRequestForms::DateRangeOverlapValidator, on: :update
  validates_with HolidayRequestForms::DestroyValidator, on: :destroy
end
