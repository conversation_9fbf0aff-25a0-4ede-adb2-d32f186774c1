Witaj<br />

<% if @outcome == 'zaakceptowany' %>
  Wniosek niedostępności użytkownika <%= @applicant_username %> został <b>zaakceptowany</b>.<br />
  <%= render partial: 'mailers/hr/holiday_request_mailer/dates', locals: { holiday_request: @holiday_request } %>
  <br />
<% elsif @outcome == 'odrzucony' %>
  Wniosek niedostępności użytkownika <%= @applicant_username %> został <b>odrzucony</b>.<br />
  <%= render partial: 'mailers/hr/holiday_request_mailer/dates', locals: { holiday_request: @holiday_request } %>
  <br />
<% else @outcome == 'zaktualizowny' %>
  Szczegóły wniosku niedostępności użytkownika <%= @applicant_username %> zostały zmienione.<br />
  <%= render partial: 'mailers/hr/holiday_request_mailer/dates', locals: { holiday_request: @holiday_request } %>
  <br />
<% end %>

<% if @mail_to_examiner %>
  <% if @holiday_request.pending? %>
    Kliknij <%= link_to 'link', root_url + "#/holidays/team_calendar/#{@holiday_request.id}" %>, aby rozpatrzyć wniosek.<br />
  <% else %>
    Kliknij <%= link_to 'link', root_url + "#/holidays/team_calendar/#{@holiday_request.id}" %>, aby zobaczyć zmiany.<br />
  <% end %>
<% else %>
  Kliknij <%= link_to 'link', root_url + "#/holidays/my_holidays/#{@holiday_request.id}" %>, aby zobaczyć zmiany.<br />
<% end %>

Jeżeli potrzebujesz pomocy napisz email <NAME_EMAIL><br />

Pozdrawiamy
