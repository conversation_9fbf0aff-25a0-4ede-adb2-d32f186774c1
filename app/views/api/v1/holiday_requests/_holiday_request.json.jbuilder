holiday_request = holiday_request.decorate unless holiday_request.decorated?
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: HolidayRequestDecorator.attribute_names_visible_to_all,
  resource: holiday_request
}
json.applicant_name h(holiday_request.applicant_name)
json.examiner_name h(holiday_request.examiner_name)
json.url holiday_request_url(holiday_request, format: :json) if holiday_request.persisted?
json.status holiday_request.status
json.cache_ts holiday_request.cache_ts
if holiday_request.actions_for_current_user['overuse_of_holidays']
  json.overuse_of_holidays @ids_of_users_who_overuse_holidays.include?(holiday_request.applicant_id) if @ids_of_users_who_overuse_holidays
end
json.actions_for_current_user holiday_request.actions_for_current_user
