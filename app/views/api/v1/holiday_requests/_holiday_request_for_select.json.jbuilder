holiday_request = holiday_request.decorate unless holiday_request.decorated?
json.cache! holiday_request do
  json.partial! '/api/v1/escape_json', locals: {
    model_attributes: HolidayRequestDecorator.attribute_names_for_collection_for_select_visible_to_all,
    resource: holiday_request
  }
  json.url holiday_request_url(holiday_request, format: :json) if holiday_request.persisted?
  json.cache_ts holiday_request.cache_ts
end
