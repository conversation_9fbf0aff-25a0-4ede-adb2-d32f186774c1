department = department.decorate unless department.decorated?
# json.extract! department, *(::DepartmentDecorator.attribute_names_for_collection_for_select_visible_to_all)
# NOTE: sanitizaton takes long time, must be cached
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: DepartmentDecorator.attribute_names_for_collection_for_select_visible_to_all,
  resource: department
}
json.company_name controller.view_context.angular_escape(h(department.company.name)) if department.association_instance_get(:company).present? && department.company
json.mpk_key department.mpk_number&.key
json.cache_ts((Time.current.to_f * 1000).ceil)
