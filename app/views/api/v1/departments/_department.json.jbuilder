department = department.decorate unless department.decorated?
json.cache! department do
  json.partial! '/api/v1/escape_json', locals: {
    model_attributes: DepartmentDecorator.attribute_names_visible_to_all,
    resource: department
  }
  json.company_name controller.view_context.angular_escape(h(department.company.name)) if (action_name == 'show' || department.association_instance_get(:company).present?) && department.company
  # nie chcialem n+1 ale <PERSON>k zrobi wszystko na indeksie i musze to zrobic (prawdopodo<PERSON>nie tymczasowo)
  json.uber_chief_name controller.view_context.angular_escape(h(department.uber_chief.full_name)) if department.uber_chief # if (action_name == 'show' || department.association_instance_get(:uber_chief).present?) && department.uber_chief
  json.chief_name controller.view_context.angular_escape(h(department.chief.full_name)) if department.chief # if (action_name == 'show' || department.association_instance_get(:chief).present?) && department.chief
  json.substitute_chief_name controller.view_context.angular_escape(h(department.substitute_chief.full_name)) if department.substitute_chief # if (action_name == 'show' || department.association_instance_get(:substitute_chief).present?) && department.substitute_chief
  json.supervisor_name department.supervisor&.full_name
  json.url department_url(department, format: :json)
  json.cache_ts((Time.current.to_f * 1000).ceil)
end
