json.partial! 'evaluation', evaluation: @evaluation
json.extract! @evaluation, :additional_user_ids, :main_answers
json.main_survey_name @evaluation.main_survey.name
json.user_full_name @user.full_name
json.additional_users do
  json.array! @evaluation.additional_users do |additional_user|
    json.extract! additional_user, :id, :full_name
  end
end
json.additional_survey_answers do
  json.array! @additional_survey_answers do |survey_answer|
    json.extract! survey_answer, :id, :kind, :state, :user_id
    json.user_full_name survey_answer.user.full_name
  end
end
