json.array! @payment_schedules do |payment_schedule|
  project = payment_schedule.project
  payments = payment_schedule.payments

  json.extract! payment_schedule, :id
  json.project do
    json.extract! project, :id, :name, :identifier
  end
  json.payments do
    json.array! payments do |payment|
      json.extract! payment, :id, :issued_on, :sell_date, :description, :predicted_amount,
                    :cyclic, :cycle_length, :originator_id, :ends_on, :kind, :currency
      json.shared payment.payment_schedule_id != payment_schedule.id
    end
  end
end
