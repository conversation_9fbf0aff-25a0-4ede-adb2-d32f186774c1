json.invoice do
  json.partial! 'api/v1/invoices/invoice', locals: { invoice: @invoice }
  json.amendment @invoice.amendment?
  json.currency @invoice.payment.currency
end

json.secondary_data do
  json.partial! 'api/v1/invoices/secondary_data'
end

json.revenue_accounts do
  json.array! @revenue_accounts do |revenue_account|
    json.extract!(revenue_account, :id, :key, :name)
  end
end

json.mpk_numbers do
  json.array! @mpk_numbers do |mpk_number|
    json.extract!(mpk_number.decorate, :id, :full_name)
  end
end

json.tax_rates do
  json.array! @tax_rates do |tax_rate|
    json.key tax_rate
    json.name InvoicePosition.human_attribute_name("tax_rate.#{tax_rate}")
  end
end

json.attachments do
  json.array! @invoice.attachments do |attachment|
    json.extract! attachment, :id
    json.file JSON.parse(attachment.file_data)
    json.file_url file_attachments_url(attachment)
    json.required @invoice.attachment_required?(attachment)
  end
end

json.required_attachments do
  json.array! @invoice.required_attachments do |attachment|
    json.extract! attachment, :id, :attachment_id
  end
end

json.kinds do
  json.array! @kinds do |kind|
    json.key kind
    json.name Invoice.human_attribute_name("kind.#{kind}")
  end
end

json.advance_invoices do
  json.array! @advance_invoices do |advance_invoice|
    json.extract! advance_invoice, :id, :number
  end
end
