# https://docs.nextcloud.com/server/12/developer_manual/core/ocs-share-api.html
class OwncloudProvider
  include HTTParty
  PROPFIND_PROPERTIES_BODY = <<~XML.squish
    <?xml version="1.0"?>
    <d:propfind  xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns" xmlns:nc="http://nextcloud.org/ns">
      <d:prop>
            <d:getlastmodified />
            <d:getetag />
            <d:getcontenttype />
            <d:resourcetype />
            <oc:fileid />
            <oc:permissions />
            <oc:size />
            <d:getcontentlength />
            <nc:has-preview />
            <oc:favorite />
            <oc:comments-unread />
            <oc:owner-display-name />
            <oc:share-types />
            <nc:contained-folder-count />
            <nc:contained-file-count />
      </d:prop>
    </d:propfind>
  XML

  FileInfo = Struct.new(:id, :file_name)

  attr_reader :config

  def initialize(name)
    @name = name
    @config = Settings[name]
    if config_valid?
      self.class.base_uri config['url']
    else
      Sidekiq.logger.warn('[OWNCLOUD] invalid configuration!')
    end
  end

  def create_directory(identifier, permissions, subdirectories: [])
    create_folder(identifier, permissions, subdirectories)
  end

  def update_directory_share(identifier, level)
    update_share(get_share_id(identifier), level)
  end

  def share_folder(identifier, permissions)
    get_group(identifier)
    path = '/ocs/v1.php/apps/files_sharing/api/v1/shares'
    data = "path=projects%2F#{identifier}&shareType=1&shareWith=#{identifier}"\
           "&permissions=#{permissions}"
    response = call_api(:post, path, data)
    code = get_from_response(response.body, 'statuscode').to_i
    handle_error(response, code, identifier) if code != 100
    logger.info('[OWNCLOUD] permissions successfully added!')
  end

  def handle_error(response, code, identifier)
    logger.info(response)
    raise "[OWNCLOUD][#{code}][#{identifier}] permissions not created!" if code != 100
  end

  def send_employees_report(report)
    path = '/remote.php/webdav/projects/projekty-dokumenty/kosztorys_stanowiska'
    call_api(:put, path + '/active_employees_report.csv', report)
    logger.info('[OWNCLOUD] report successfully saved!')
  end

  def send_file(file, path, file_name)
    full_path = "/remote.php/webdav/projects/#{encode(path)}/"
    call_api(:mkcol, full_path)
    call_api(:put, full_path + encode(file_name), file.read)
    logger.info("[OWNCLOUD] file #{file_name} succesfully uploaded to #{path}!")
  end

  def config_valid?
    config.present? &&
      config['url'].present? &&
      config['username'].present? &&
      config['password'].present?
  end

  def get_project_files(identifier, folder_name)
    path = "/remote.php/webdav/projects/#{encode(identifier)}/#{encode(folder_name)}"
    result = call_api(:propfind, path, PROPFIND_PROPERTIES_BODY)
    return [] if result.code >= 400

    get_project_files_from_xml(result.body)
  end

  private

  def get_project_files_from_xml(xml)
    hash = Hash.from_xml(xml).with_indifferent_access
    relevant_info = filter_files(hash)
    relevant_info.map do |info|
      FileInfo.new(
        info[:propstat].first[:prop][:fileid],
        CGI.unescape(File.basename(info[:href].strip))
      )
    end
  end

  def encode(string)
    ERB::Util.url_encode(string)
  end

  def filter_files(hash)
    [hash[:multistatus][:response]].flatten.reject do |response|
      response[:propstat].first[:prop][:resourcetype]
    end
  end

  def get_group(identifier)
    path = '/ocs/v1.php/cloud/groups/' + identifier
    call_api :get, path
  end

  def update_share(share_id, permissions)
    path = "/ocs/v1.php/apps/files_sharing/api/v1/shares/#{share_id}"
    body = "permissions=#{permissions}"
    response = call_api(:put, path, body)
    code = get_from_response(response.body, 'statuscode').to_i
    message = response.dig('ocs', 'meta', 'message')
    if message == 'Path is already shared with this group'
      logger.info "[OWNCLOUD][UPDATE_SHARE] Share #{share_id} with permissions #{permissions} already exists!"
    else
      raise get_from_response(message, 'message') if code != 100
      logger.info "[OWNCLOUD][UPDATE_SHARE] Share #{share_id} successfully updated!"
    end
  end

  def get_share_id(identifier)
    path = "/ocs/v1.php/apps/files_sharing/api/v1/shares?path=projects/#{identifier}"
    response = call_api(:get, path)
    code = get_from_response(response.body, 'statuscode').to_i
    if code == 100
      get_from_response(response.body, 'id').to_i
    else
      raise "[OWNCLOUD][GET_SHARE][#{identifier}] could not get folder share_id!"
    end
  end

  def create_folder(identifier, permissions, subdirectories)
    path = "/remote.php/webdav/projects/#{identifier}/"
    response = call_api(:mkcol, path)
    create_subdirectories(path, subdirectories) if response.code == 201
    logger.info('[OWNCLOUD] folder created successfully!')
    worker_class.perform_in(1.minute, 'share_folder', identifier, permissions)
  end

  def create_subdirectories(path, subdirectories)
    subdirectories.each do |subdirectory, nested_subdirectories|
      nested_path = "#{path}#{ERB::Util.url_encode(subdirectory)}/"
      call_api(:mkcol, nested_path)
      create_subdirectories(nested_path, nested_subdirectories) if nested_subdirectories.present?
    end
  end

  def call_api(action, path, body = {})
    self.class.send(action, path, basic_auth: basic_auth_headers, body: body, headers: ocs_headers)
  end

  def get_from_response(body, key)
    doc = Nokogiri::XML(body)
    doc.xpath("//#{key}").first.content
  end

  def logger
    Rails.logger
  end

  def basic_auth_headers
    { username: config['username'], password: config['password'] }
  end

  def ocs_headers
    { "OCS-APIRequest" => "true" }
  end

  def worker_class
    @name == 'docscloud' ? DocscloudWorker : OwncloudWorker
  end
end
