require "test_helper"

module Api
  module V1
    class InvoicesControllerTest < ActionController::TestCase
      setup do
        @user = users(:wik<PERSON>)
        authenticate(@user)
        @project = projects(:two)
      end

      test 'index' do
        acceptable_invoice_order = ['Payment three amendment', 'Payment three invoice',
                                    'Payment four invoice', 'Payment four amendment',
                                    'Ten payment invoice', 'Payment five draft']
        get :index, params: { project_id: @project.id }, format: :json
        assert_response :success
        assert_equal acceptable_invoice_order.count, json_body.size
        assert_equal acceptable_invoice_order, (json_body.collect { |i| i['title'] })
      end

      test 'index with kind invoice filter' do
        accounting_note = invoices(:payment_four)
        accounting_note.update_columns(kind: :accounting_note)

        get :index, params: { project_id: @project.id, f: { kind: 'invoice' } }, format: :json

        assert_response :success
        assert_equal 5, json_body.size
        refute_includes json_body.pluck('id'), accounting_note.id
      end

      test 'index with kind accounting_note filter' do
        accounting_note = invoices(:payment_three)
        accounting_note.update_columns(kind: :accounting_note)

        get :index, params: { project_id: @project.id, f: { kind: 'accounting_note' } }, format: :json

        assert_response :success
        assert_equal 1, json_body.size
        assert_includes json_body.pluck('id'), accounting_note.id
      end

      test 'show' do
        invoice = invoices(:payment_three)
        get :show, params: { project_id: @project.id, id: invoice.id }, format: :json
        assert_response :success
        assert_equal invoice.id, json_body['id']
        assert_equal invoice.title, json_body['title']
        refute_empty json_body['mpk_positions_attributes']
      end

      test 'show shared invoice' do
        invoice = invoices(:payment_ten_amendment)

        get :show, params: { project_id: @project.id, id: invoice.id }, format: :json

        assert_response :success
        assert_equal invoice.id, json_body['id']
        assert json_body['shared']
      end

      test 'show includes JPK attributes of associated invoice position' do
        invoice = invoices(:payment_three)
        get :show, params: { project_id: @project.id, id: invoice.id }, format: :json
        refute_nil json_body['invoice_positions_attributes'][0]['jpk_gtu']
        refute_nil json_body['invoice_positions_attributes'][0]['jpk_transaction_code']

      end

      test 'form_data returns proper data for invoiceless payment' do
        payment = payments(:six)
        get :form_data, params: { project_id: @project.id, payment_id: payment.id },
                        format: :json
        assert_response :success
        assert_equal MpkNumber.count, json_body['mpk_numbers'].size
        assert_equal RevenueAccount.count, json_body['revenue_accounts'].size
        assert_equal @project.name, json_body['secondary_data']['project']['name']
        assert_equal false, json_body['invoice']['draft']
        assert_equal payment.issued_on.to_s, json_body['invoice']['invoice_date']
        assert_equal payment.sell_date.to_s, json_body['invoice']['sell_date']
      end

      test 'form_data returns proper data for invoiceless payment, but payment has mpk positions' do
        payment = payments(:six)
        mpk_number = mpk_numbers(:motion_design)
        PaymentMpkPosition.create!(payment: payment, mpk_number: mpk_number, amount: 8000)

        get :form_data, params: { project_id: @project.id, payment_id: payment.id },
                        format: :json
        assert_response :success
        assert_equal MpkNumber.count, json_body['mpk_numbers'].size
        assert_equal RevenueAccount.count, json_body['revenue_accounts'].size
        assert_equal @project.name, json_body['secondary_data']['project']['name']
        assert_equal false, json_body['invoice']['draft']
        assert_equal payment.mpk_positions.count, json_body['invoice']['mpk_positions_attributes'].size
      end

      test 'form_data returns proper data for payment with invoice' do
        payment = payments(:three)
        get :form_data, params: { project_id: @project.id, payment_id: payment.id },
                        format: :json
        assert_response :success
        assert_equal MpkNumber.count, json_body['mpk_numbers'].size
        assert_equal @project.name, json_body['secondary_data']['project']['name']
        assert_equal true, json_body['invoice']['amendment']
        assert_equal true, json_body['invoice']['draft']
      end

      test 'form_data returns proper kinds for invoice kind payment' do
        payment = payments(:six)
        get :form_data, params: { project_id: @project.id, payment_id: payment.id }, format: :json

        assert_response :success
        assert_equal %w[vat re_invoice advance advance_accounting vat_barter], json_body['kinds'].pluck('key')
      end

      test 'form_data returns proper kinds for accounting note kind payment' do
        payment = payments(:six)
        payment.update_columns(kind: :accounting_note)
        get :form_data, params: { project_id: @project.id, payment_id: payment.id }, format: :json

        assert_response :success
        assert_equal ['accounting_note'], json_body['kinds'].pluck('key')
      end

      test 'creates invoices with mpk positions properly' do
        assert_difference('Invoice.count') do
          assert_difference('MpkPosition.count') do
            post :create, params: { invoice: invoice_params, project_id: @project.id }, format: :json
          end
        end
        assert_response :created
        invoice = Invoice.last
        assert_equal @user.id, invoice.user_id
      end

      test 'creates invoice with present jpk position attributes properly' do
        params = invoice_params(
          invoice_positions_attributes: [
            { name: 'position name', amount: 1, unit_price: 5, tax_rate: :'23', jpk_gtu: 'GTU_11',
              jpk_transaction_code: 'SW' }
          ],
        )
        assert_difference('Invoice.count') do
          post :create, params: { invoice: params, project_id: @project.id }, format: :json
        end
        assert_response :created
        invoice = Invoice.last
        invoice_position = invoice.invoice_positions.last
        assert_equal 'GTU_11', invoice_position.jpk_gtu
        assert_equal 'SW', invoice_position.jpk_transaction_code
      end

      test 'does not create invoice given invalid params' do
        params = { title: 'example title', send_to_controller: true }
        assert_no_difference('Invoice.count') do
          post :create, params: { invoice: params, project_id: @project.id }, format: :json
        end
        assert_response :unprocessable_entity
      end

      test 'updates invoice properly' do
        invoice = invoices(:payment_three_amendment_draft)
        params = { send_to_controller: true }
        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json
        assert_response :no_content
        assert invoice.reload.pending?
      end

      test 'updates invoice with jpk position attributes properly' do
        invoice = invoices(:payment_three_amendment_draft)
        invoice_position = invoice_positions(:payment_three_amendment_draft_position)

        params = {
          invoice_positions_attributes: [
            { id: invoice_position.id, jpk_gtu: 'GTU_12', jpk_transaction_code: 'EE' }
          ]
        }

        assert_equal 'GTU_11', invoice_position.jpk_gtu
        assert_equal 'SW', invoice_position.jpk_transaction_code

        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json

        assert_response :no_content

        assert_equal 'GTU_12', invoice_position.reload.jpk_gtu
        assert_equal 'EE', invoice_position.jpk_transaction_code
      end

      test 'updates invoice with required_attachments' do
        invoice = invoices(:payment_five_draft)

        attachment_one = attachments(:one)
        attachment_two = attachments(:two)

        params = {
          attachments_required: true,
          required_attachments_attributes: [
            { attachment_id: attachment_one.id },
            { attachment_id: attachment_two.id }
          ]
        }

        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json

        assert_response :no_content

        invoice.reload

        assert_equal 2, invoice.required_attachments.count
      end

      test 'updates issued invoice with required_attachments' do
        invoice = invoices(:payment_five_draft)

        attachment_one = attachments(:one)
        attachment_two = attachments(:two)

        invoice.send_to_controller!
        invoice.accept!(users(:wiktoria))
        invoice.issue!(users(:wiktoria))

        params = {
          attachments_required: true,
          required_attachments_attributes: [
            { attachment_id: attachment_one.id },
            { attachment_id: attachment_two.id }
          ]
        }

        assert invoice.issued?

        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json

        assert_response :no_content

        invoice.reload

        assert_equal 2, invoice.required_attachments.count
      end

      test 'updates invoice with required_attachments (should exists but doesn\'t)' do
        invoice = invoices(:payment_five_draft)

        params = {
          send_to_controller: true,
          attachments_required: true,
          required_attachments_attributes: []
        }

        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json

        assert_response :unprocessable_entity
      end

      test 'updates invoice with required_attachments (invalid attachment id)' do
        invoice = invoices(:payment_five_draft)

        params = {
          send_to_controller: true,
          attachments_required: true,
          required_attachments_attributes: [
            { attachment_id: 1234 }
          ]
        }

        assert_raises(ActiveRecord::InvalidForeignKey) do
          patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                         format: :json
        end
      end

      test 'updates invoice (doens\'t persist required_attachments if attachments_required is false)' do
        invoice = invoices(:payment_five_draft)
        invoice.update(attachments_required: false)

        attachment_one = attachments(:one)
        attachment_two = attachments(:two)

        params = {
          required_attachments_attributes: [
            { attachment_id: attachment_one.id },
            { attachment_id: attachment_two.id }
          ]
        }

        patch :update, params: { invoice: params, project_id: @project.id, id: invoice.id },
                       format: :json

        assert_response :no_content

        invoice.reload

        assert_equal 0, invoice.required_attachments.count
      end

      private

      def invoice_params(custom = {})
        {
          payment_id: payments(:six).id, title: 'example invoice',
          file_name: 'example file name', sell_date: Time.zone.today,
          invoice_date: Time.zone.today, due_date: Time.zone.today + 14,
          total_amount: 500, receiver_name: 'Name', send_to_controller: true, no_attachment: true,
          mpk_positions_attributes: [
            { mpk_number_id: mpk_numbers(:other).id, amount: 500 }
          ],
          invoice_positions_attributes: [
            { name: 'Position name', amount: 1, unit_price: 5, tax_rate: :'23' }
          ],
          revenue_account_id: revenue_accounts(:service_build).id,
          bank_account_id: bank_accounts(:arte_account).id
        }.merge(custom)
      end
    end
  end
end
