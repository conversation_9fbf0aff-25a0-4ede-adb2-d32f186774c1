require 'test_helper'
require 'roo'

class UnpaidInvoicesXlsxReaderTest < ActiveSupport::TestCase
  setup do
    @client = clients(:arte)
    @client.update!(vat_number: '1234567890', invoice_sending_email: '<EMAIL>')

    mock_body = mock('body')
    mock_body.stubs(:raw_source).returns('<p>Test email content</p>')

    mock_html_part = mock('html_part')
    mock_html_part.stubs(:body).returns(mock_body)

    mock_mail = mock('mail')
    mock_mail.stubs(:html_part).returns(mock_html_part)

    InvoicesMailer.stubs(:unpaid_invoices_to_five).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_six_to_thirty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_thirty_one_to_sixty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_sixty_one_to_one_twenty).returns(mock_mail)
    InvoicesMailer.stubs(:unpaid_invoices_over_one_twenty).returns(mock_mail)
  end

  test 'reads valid XLSX file with unpaid invoices' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 1000.0,
        end_sum: 1000.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length

    invoice_data = result.first
    assert_equal 'Artegence', invoice_data[:client]
    assert_equal ['unpaid_invoices_to_five'], invoice_data[:overdue_statuses]
    assert_equal 'Test Company', invoice_data[:company]
    assert_equal 'PLN', invoice_data[:currency]
    assert_equal 1000.0, invoice_data[:amount]
    assert_equal ['INV-001'], invoice_data[:invoices_numbers]
    assert_equal '<EMAIL>', invoice_data[:emails]
    assert invoice_data[:checked]
    assert_not_nil invoice_data[:content]
  end

  test 'groups multiple invoices by client' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length

    assert_equal 800.0, result.first[:amount]
    assert_equal %w[INV-001 INV-002], result.first[:invoices_numbers]
    assert_equal %w[unpaid_invoices_to_five unpaid_invoices_six_to_thirty], result.first[:overdue_statuses]
  end

  test 'skips paid invoices' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: 'PAID',
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        to_five: 300.0,
        end_sum: 300.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length
    assert_equal ['INV-002'], result.first[:invoices_numbers]
  end

  test 'skips invoices with blank VAT number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'skips invoices with blank invoice number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: '',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'handles client not found by VAT number' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: 'NONEXISTENT',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_empty result
  end

  test 'handles client with blank email' do
    @client.update!(invoice_sending_email: '')

    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length
    assert_equal '', result.first[:emails]
    assert_not result.first[:checked]
  end

  test 'raises error when headers not found' do
    mock_file = mock('file')
    mock_file.stubs(:path).returns('/tmp/test.xlsx')

    mock_xlsx = mock('xlsx')
    mock_xlsx.stubs(:sheets).returns(['Sheet1'])
    mock_xlsx.stubs(:default_sheet=).with('Sheet1')

    mock_xlsx.stubs(:each_row_streaming).returns([['row1'], ['row2']])

    mock_xlsx.stubs(:row).with(1).returns(['Column1', 'Column2', 'Column3'])
    mock_xlsx.stubs(:row).with(2).returns(['Data1', 'Data2', 'Data3'])
    (3..30).each { |i| mock_xlsx.stubs(:row).with(i).returns([]) }

    Roo::Excelx.stubs(:new).with('/tmp/test.xlsx').returns(mock_xlsx)

    error = assert_raises(RuntimeError) do
      UnpaidInvoicesXlsxReader.new(mock_file)
    end

    assert_equal 'Headers not found', error.message
  end

  test 'sorts results by client name and overdue status' do
    client_b = clients(:polexit)
    client_b.update!(vat_number: '9876543210', invoice_sending_email: '<EMAIL>', street_number: 6)

    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '9876543210',
        invoice_number: 'INV-003',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 200.0,
        end_sum: 200.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 500.0,
        end_sum: 500.0
      },
      {
        company: 'Test Company',
        vat: '9876543210',
        invoice_number: 'INV-004',
        currency: 'PLN',
        is_paid: nil,
        to_five: 100.0,
        end_sum: 100.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 2, result.length

    assert_equal @client.name, result[0][:client]
    assert_equal ['unpaid_invoices_to_five'], result[0][:overdue_statuses]

    assert_equal client_b.name, result[1][:client]
    assert_includes result[1][:overdue_statuses], 'unpaid_invoices_to_five'
    assert_includes result[1][:overdue_statuses], 'unpaid_invoices_six_to_thirty'
  end

  test 'handles all overdue status types' do
    file = create_test_xlsx_file([
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-001',
        currency: 'PLN',
        is_paid: nil,
        to_five: 100.0,
        end_sum: 100.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-002',
        currency: 'PLN',
        is_paid: nil,
        six_to_thirty: 200.0,
        end_sum: 200.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-003',
        currency: 'PLN',
        is_paid: nil,
        thirty_one_to_sixty: 300.0,
        end_sum: 300.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-004',
        currency: 'PLN',
        is_paid: nil,
        sixty_one_to_one_twenty: 400.0,
        end_sum: 400.0
      },
      {
        company: 'Test Company',
        vat: '1234567890',
        invoice_number: 'INV-005',
        currency: 'PLN',
        is_paid: nil,
        over_one_twenty: 500.0,
        end_sum: 500.0
      }
    ])

    reader = UnpaidInvoicesXlsxReader.new(file)
    result = reader.read

    assert_equal 1, result.length

    expected_statuses = %w[unpaid_invoices_to_five unpaid_invoices_six_to_thirty unpaid_invoices_thirty_one_to_sixty unpaid_invoices_sixty_one_to_one_twenty unpaid_invoices_over_one_twenty]

    assert_equal expected_statuses, result.first[:overdue_statuses]
  end

  private

  def create_test_xlsx_file(data)
    require 'caxlsx'

    package = Axlsx::Package.new
    workbook = package.workbook

    workbook.add_worksheet(name: "Test") do |sheet|
      5.times { sheet.add_row([]) }

      sheet.add_row([
        'Baza Firmowa',
        'Podmiot Nazwa',
        'Podmiot Kod',
        'Dokument Numer',
        'Data Dokumentu',
        'Data Realizacji',
        'Waluta',
        'Terminowe',
        'Empty1',
        '2. Przeterminowane nie więcej niż 5 dni',
        'Empty2',
        '3. Przeterminowane od 6 do 30 dni',
        'Empty3',
        '4. Przeterminowane od 31 do 60 dni',
        'Empty4',
        '5. Przeterminowane od 60 do 120 dni',
        'Empty5',
        '6. Przeterminowane powyżej 120 dni',
        'Empty6',
        'Suma końcowa'
      ])

      data.each do |row_data|
        sheet.add_row([
          row_data[:company],
          'Client Name',
          row_data[:vat],
          row_data[:invoice_number],
          '2025-01-01',
          '2025-01-15',
          row_data[:currency],
          row_data[:is_paid],
          '',
          row_data[:to_five],
          '',
          row_data[:six_to_thirty],
          '',
          row_data[:thirty_one_to_sixty],
          '',
          row_data[:sixty_one_to_one_twenty],
          '',
          row_data[:over_one_twenty],
          '',
          row_data[:end_sum]
        ])
      end
    end

    temp_file = Tempfile.new(['test_invoices', '.xlsx'])
    temp_file.binmode
    temp_file.write(package.to_stream.read)
    temp_file.rewind

    Rack::Test::UploadedFile.new(temp_file.path, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'test_invoices.xlsx')
  end
end
